import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../app/router/routes.dart';
import '../../../../core/constants/ui_constants.dart';
import '../../../../shared/widgets/responsive_layout.dart';
import '../../../../shared/models/app_error.dart';
import '../../providers/web_auth_providers.dart';

/// [EmailVerificationPage] handles email verification from email links.
///
/// This page processes email verification tokens from verification links
/// and provides feedback to users about the verification status.
class EmailVerificationPage extends ConsumerStatefulWidget {
  /// The verification token from the email link
  final String? token;

  const EmailVerificationPage({
    super.key,
    this.token,
  });

  @override
  ConsumerState<EmailVerificationPage> createState() =>
      _EmailVerificationPageState();
}

class _EmailVerificationPageState extends ConsumerState<EmailVerificationPage> {
  bool _isVerifying = false;
  bool _isVerified = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _verifyEmail();
  }

  Future<void> _verifyEmail() async {
    if (widget.token == null || widget.token!.isEmpty) {
      setState(() {
        _errorMessage = 'Invalid verification link. Please try again.';
      });
      return;
    }

    setState(() {
      _isVerifying = true;
      _errorMessage = null;
    });

    try {
      final notifier = ref.read(webAuthNotifierProvider.notifier);
      final result = await notifier.verifyEmail(widget.token!);

      if (mounted) {
        result.when(
          success: (_) {
            setState(() {
              _isVerifying = false;
              _isVerified = true;
            });

            // Auto-redirect to dashboard after 3 seconds
            Future.delayed(const Duration(seconds: 3), () {
              if (mounted) {
                context.go(AppRoutes.home);
              }
            });
          },
          failure: (error) {
            setState(() {
              _isVerifying = false;
              _errorMessage = error.userMessage;
            });
          },
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isVerifying = false;
          _errorMessage = 'Verification failed. Please try again.';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Verification'),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(context),
        desktop: _buildDesktopLayout(context),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.spacing24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Padding(
          padding: const EdgeInsets.all(UIConstants.spacing48),
          child: _buildContent(context),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final theme = Theme.of(context);

    if (_isVerifying) {
      return _buildVerifyingState(theme);
    } else if (_isVerified) {
      return _buildSuccessState(theme);
    } else if (_errorMessage != null) {
      return _buildErrorState(theme);
    } else {
      return _buildInitialState(theme);
    }
  }

  Widget _buildVerifyingState(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: UIConstants.spacing24),
        Text(
          'Verifying your email...',
          style: theme.textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: UIConstants.spacing16),
        Text(
          'Please wait while we verify your email address.',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSuccessState(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.check_circle,
          size: 80,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: UIConstants.spacing24),
        Text(
          'Email Verified!',
          style: theme.textTheme.headlineSmall?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: UIConstants.spacing16),
        Text(
          'Your email has been successfully verified. You will be redirected to your dashboard shortly.',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: UIConstants.spacing32),
        FilledButton(
          onPressed: () => context.go(AppRoutes.home),
          child: const Text('Go to Dashboard'),
        ),
      ],
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.error_outline,
          size: 80,
          color: theme.colorScheme.error,
        ),
        const SizedBox(height: UIConstants.spacing24),
        Text(
          'Verification Failed',
          style: theme.textTheme.headlineSmall?.copyWith(
            color: theme.colorScheme.error,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: UIConstants.spacing16),
        Text(
          _errorMessage ?? 'An error occurred during verification.',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: UIConstants.spacing32),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            OutlinedButton(
              onPressed: () => context.go(AppRoutes.login),
              child: const Text('Back to Login'),
            ),
            FilledButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInitialState(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.email_outlined,
          size: 80,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: UIConstants.spacing24),
        Text(
          'Email Verification',
          style: theme.textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: UIConstants.spacing16),
        Text(
          'Processing your email verification...',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
