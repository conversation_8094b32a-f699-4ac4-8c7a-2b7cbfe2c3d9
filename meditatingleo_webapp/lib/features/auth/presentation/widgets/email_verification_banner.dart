import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/constants/ui_constants.dart';
import '../../../../shared/models/app_error.dart';
import '../../providers/web_auth_providers.dart';

/// [EmailVerificationBanner] displays a banner encouraging email verification.
///
/// This banner is shown to users who have not yet verified their email address.
/// It provides options to resend verification email and dismiss the banner.
class EmailVerificationBanner extends ConsumerStatefulWidget {
  /// The user's email address
  final String email;

  /// Callback when banner is dismissed
  final VoidCallback? onDismiss;

  const EmailVerificationBanner({
    super.key,
    required this.email,
    this.onDismiss,
  });

  @override
  ConsumerState<EmailVerificationBanner> createState() =>
      _EmailVerificationBannerState();
}

class _EmailVerificationBannerState
    extends ConsumerState<EmailVerificationBanner> {
  bool _isResending = false;
  bool _isDismissed = false;
  String? _successMessage;

  @override
  Widget build(BuildContext context) {
    if (_isDismissed) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(UIConstants.spacing16),
      margin: const EdgeInsets.all(UIConstants.spacing16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(UIConstants.borderRadius12),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.email_outlined,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: UIConstants.spacing12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Verify your email address',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: UIConstants.spacing4),
                    Text(
                      'Please check your email and click the verification link to secure your account.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer
                            .withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _dismissBanner,
                icon: Icon(
                  Icons.close,
                  color: theme.colorScheme.onPrimaryContainer,
                ),
                tooltip: 'Dismiss',
              ),
            ],
          ),
          if (_successMessage != null) ...[
            const SizedBox(height: UIConstants.spacing12),
            Container(
              padding: const EdgeInsets.all(UIConstants.spacing8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(UIConstants.borderRadius8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 16,
                  ),
                  const SizedBox(width: UIConstants.spacing8),
                  Expanded(
                    child: Text(
                      _successMessage!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: UIConstants.spacing16),
          Row(
            children: [
              FilledButton.icon(
                onPressed: _isResending ? null : _resendVerificationEmail,
                icon: _isResending
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh, size: 18),
                label: Text(_isResending ? 'Sending...' : 'Resend Email'),
                style: FilledButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                ),
              ),
              const SizedBox(width: UIConstants.spacing12),
              TextButton(
                onPressed: _dismissBanner,
                child: Text(
                  'Remind me later',
                  style: TextStyle(
                    color: theme.colorScheme.onPrimaryContainer
                        .withValues(alpha: 0.8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _resendVerificationEmail() async {
    if (_isResending) return;

    setState(() {
      _isResending = true;
      _successMessage = null;
    });

    try {
      final notifier = ref.read(webAuthNotifierProvider.notifier);
      final result = await notifier.resendEmailVerification(widget.email);

      if (mounted) {
        result.when(
          success: (_) {
            setState(() {
              _isResending = false;
              _successMessage = 'Verification email sent! Check your inbox.';
            });
          },
          failure: (error) {
            setState(() {
              _isResending = false;
            });

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(error.userMessage),
                  backgroundColor: Theme.of(context).colorScheme.error,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          },
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isResending = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
                'Failed to send verification email. Please try again.'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _dismissBanner() {
    setState(() {
      _isDismissed = true;
    });
    widget.onDismiss?.call();
  }
}
