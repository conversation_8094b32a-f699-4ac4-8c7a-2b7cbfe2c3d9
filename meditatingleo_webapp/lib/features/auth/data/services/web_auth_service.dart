import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/web_user_model.dart';
import '../models/auth_session_model.dart';
import '../../../../shared/models/result.dart';
import '../../../../shared/models/app_error.dart';

/// [WebAuthService] handles Supabase authentication for the web application.
///
/// This service provides methods for user authentication, session management,
/// and user account operations specific to the web platform.
class WebAuthService {
  final SupabaseClient _supabase;

  const WebAuthService(this._supabase);

  /// Signs in a user with email and password.
  Future<Result<(WebUserModel, AuthSessionModel), AppError>> signInWithEmail(
    String email,
    String password,
  ) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null || response.session == null) {
        return Result.failure(
          AppError.authentication('Authentication failed'),
        );
      }

      final user = _mapToWebUser(response.user!);
      final session = _mapToAuthSession(response.session!);

      return Result.success((user, session));
    } on AuthException catch (e) {
      return Result.failure(
        AppError.authentication(e.message),
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Sign in failed: ${e.toString()}'),
      );
    }
  }

  /// Signs up a new user with email, password, and name.
  ///
  /// **Handles both Supabase email verification configurations:**
  ///
  /// **Email Verification DISABLED (Supabase setting):**
  /// - Returns: (user, session) - User is immediately logged in
  /// - Flow: Register → Auto-login → Dashboard
  /// - No email verification required
  ///
  /// **Email Verification ENABLED (Supabase setting - default):**
  /// - Returns: (user, null) - No session until email is verified
  /// - Flow: Register → Login page → Verify email → Manual login → Dashboard
  /// - Email verification required before login
  ///
  /// The return value indicates which configuration is active:
  /// - If session is not null: Email verification is disabled
  /// - If session is null: Email verification is enabled
  Future<Result<(WebUserModel, AuthSessionModel?), AppError>> signUp(
    String email,
    String password,
    String? name,
  ) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: name != null ? {'name': name} : null,
      );

      if (response.user == null) {
        return Result.failure(
          AppError.authentication('Sign up failed'),
        );
      }

      final user = _mapToWebUser(response.user!);

      // Check if session is available (auto-login)
      AuthSessionModel? session;
      if (response.session != null) {
        session = _mapToAuthSession(response.session!);
      }

      return Result.success((user, session));
    } on AuthException catch (e) {
      return Result.failure(
        AppError.authentication(e.message),
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Sign up failed: ${e.toString()}'),
      );
    }
  }

  /// Signs out the current user.
  Future<Result<void, AppError>> signOut() async {
    try {
      await _supabase.auth.signOut();
      return Result.success(null);
    } on AuthException catch (e) {
      return Result.failure(
        AppError.authentication(e.message),
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Sign out failed: ${e.toString()}'),
      );
    }
  }

  /// Sends a password reset email.
  Future<Result<void, AppError>> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
      return Result.success(null);
    } on AuthException catch (e) {
      return Result.failure(
        AppError.authentication(e.message),
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Password reset failed: ${e.toString()}'),
      );
    }
  }

  /// Refreshes the current session.
  Future<Result<AuthSessionModel, AppError>> refreshSession() async {
    try {
      final response = await _supabase.auth.refreshSession();

      if (response.session == null) {
        return Result.failure(
          AppError.authentication('Session refresh failed'),
        );
      }

      final session = _mapToAuthSession(response.session!);
      return Result.success(session);
    } on AuthException catch (e) {
      return Result.failure(
        AppError.authentication(e.message),
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Session refresh failed: ${e.toString()}'),
      );
    }
  }

  /// Gets the current user.
  Future<Result<WebUserModel?, AppError>> getCurrentUser() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return Result.success(null);
      }

      final webUser = _mapToWebUser(user);
      return Result.success(webUser);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to get current user: ${e.toString()}'),
      );
    }
  }

  /// Gets the current session.
  Future<Result<AuthSessionModel?, AppError>> getCurrentSession() async {
    try {
      final session = _supabase.auth.currentSession;
      if (session == null) {
        return Result.success(null);
      }

      final authSession = _mapToAuthSession(session);
      return Result.success(authSession);
    } catch (e) {
      return Result.failure(
        AppError.unknown('Failed to get current session: ${e.toString()}'),
      );
    }
  }

  /// Updates the user's profile.
  Future<Result<WebUserModel, AppError>> updateProfile({
    String? name,
    String? avatarUrl,
  }) async {
    try {
      final updates = <String, dynamic>{};
      if (name != null) updates['name'] = name;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;

      final response = await _supabase.auth.updateUser(
        UserAttributes(data: updates),
      );

      if (response.user == null) {
        return Result.failure(
          AppError.authentication('Profile update failed'),
        );
      }

      final user = _mapToWebUser(response.user!);
      return Result.success(user);
    } on AuthException catch (e) {
      return Result.failure(
        AppError.authentication(e.message),
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Profile update failed: ${e.toString()}'),
      );
    }
  }

  /// Changes the user's password.
  Future<Result<void, AppError>> changePassword(String newPassword) async {
    try {
      await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return Result.success(null);
    } on AuthException catch (e) {
      return Result.failure(
        AppError.authentication(e.message),
      );
    } catch (e) {
      return Result.failure(
        AppError.unknown('Password change failed: ${e.toString()}'),
      );
    }
  }

  /// Maps Supabase User to WebUserModel.
  WebUserModel _mapToWebUser(User user) {
    return WebUserModel(
      id: user.id,
      email: user.email ?? '',
      name: user.userMetadata?['name'] as String?,
      avatarUrl: user.userMetadata?['avatar_url'] as String?,
      createdAt: DateTime.parse(user.createdAt),
      lastSignInAt:
          user.lastSignInAt != null ? DateTime.parse(user.lastSignInAt!) : null,
      emailConfirmedAt: user.emailConfirmedAt != null
          ? DateTime.parse(user.emailConfirmedAt!)
          : null,
    );
  }

  /// Maps Supabase Session to AuthSessionModel.
  AuthSessionModel _mapToAuthSession(Session session) {
    return AuthSessionModel(
      accessToken: session.accessToken,
      refreshToken: session.refreshToken,
      expiresIn: session.expiresIn ?? 3600,
      expiresAt: session.expiresAt != null
          ? DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)
          : null,
      tokenType: session.tokenType ?? 'bearer',
      userId: session.user.id,
    );
  }

  /// Resends email verification for the given email address.
  Future<Result<void, AppError>> resendEmailVerification(String email) async {
    try {
      await _supabase.auth.resend(
        type: OtpType.signup,
        email: email,
      );
      return Result.success(null);
    } on AuthException catch (e) {
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(
        AppError.unknown(
            'Failed to resend verification email: ${e.toString()}'),
      );
    }
  }

  /// Verifies email with the provided token.
  Future<Result<void, AppError>> verifyEmail(String token) async {
    try {
      final response = await _supabase.auth.verifyOTP(
        type: OtpType.signup,
        token: token,
      );

      if (response.user != null) {
        return Result.success(null);
      } else {
        return Result.failure(
          AppError.authentication('Invalid verification token'),
        );
      }
    } on AuthException catch (e) {
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(
        AppError.unknown('Email verification failed: ${e.toString()}'),
      );
    }
  }

  /// Signs in with OAuth provider.
  Future<Result<(WebUserModel, AuthSessionModel), AppError>> signInWithOAuth({
    required OAuthProvider provider,
    String? redirectTo,
  }) async {
    try {
      final response = await _supabase.auth.signInWithOAuth(
        provider,
        redirectTo: redirectTo,
      );

      if (response) {
        // OAuth flow initiated successfully
        // The actual user and session will be available after redirect
        // For now, we return a placeholder that indicates OAuth flow started
        throw const AuthException('OAuth flow initiated');
      } else {
        return Result.failure(
          AppError.authentication('Failed to initiate OAuth flow'),
        );
      }
    } on AuthException catch (e) {
      if (e.message == 'OAuth flow initiated') {
        // This is expected for OAuth flows
        rethrow;
      }
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(
        AppError.unknown('OAuth sign in failed: ${e.toString()}'),
      );
    }
  }

  /// Handles OAuth callback and completes authentication.
  Future<Result<(WebUserModel, AuthSessionModel), AppError>>
      handleOAuthCallback() async {
    try {
      final session = _supabase.auth.currentSession;
      final user = _supabase.auth.currentUser;

      if (session != null && user != null) {
        final webUser = _mapToWebUser(user);
        final authSession = _mapToAuthSession(session);
        return Result.success((webUser, authSession));
      } else {
        return Result.failure(
          AppError.authentication('OAuth authentication failed'),
        );
      }
    } catch (e) {
      return Result.failure(
        AppError.unknown('OAuth callback handling failed: ${e.toString()}'),
      );
    }
  }

  /// Gets available OAuth providers.
  List<OAuthProvider> getAvailableOAuthProviders() {
    return [
      OAuthProvider.google,
      OAuthProvider.github,
      OAuthProvider.apple,
      OAuthProvider.discord,
      OAuthProvider.facebook,
      OAuthProvider.twitter,
    ];
  }

  /// Listens to authentication state changes.
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;
}
