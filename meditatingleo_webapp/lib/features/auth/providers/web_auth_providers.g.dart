// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'web_auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webAuthServiceHash() => r'd59d04b2d6fd9eac006de2a12712743947c5b435';

/// Web authentication service provider
///
/// Copied from [webAuthService].
@ProviderFor(webAuthService)
final webAuthServiceProvider = AutoDisposeProvider<WebAuthService>.internal(
  webAuthService,
  name: r'webAuthServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webAuthServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebAuthServiceRef = AutoDisposeProviderRef<WebAuthService>;
String _$webSessionServiceHash() => r'dddb8c1a12d4e43e95a1d34f369ea52904a413b0';

/// Web session service provider
///
/// Copied from [webSessionService].
@ProviderFor(webSessionService)
final webSessionServiceProvider =
    AutoDisposeProvider<WebSessionService>.internal(
  webSessionService,
  name: r'webSessionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webSessionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebSessionServiceRef = AutoDisposeProviderRef<WebSessionService>;
String _$webAuthRepositoryHash() => r'25dbe7ba0a245ddff7ddce4816ad37b8be6b521c';

/// Web authentication repository provider
///
/// Copied from [webAuthRepository].
@ProviderFor(webAuthRepository)
final webAuthRepositoryProvider =
    AutoDisposeProvider<WebAuthRepository>.internal(
  webAuthRepository,
  name: r'webAuthRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webAuthRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebAuthRepositoryRef = AutoDisposeProviderRef<WebAuthRepository>;
String _$currentUserHash() => r'e26bd848959278e90a92a1e88ba6940a3c53a7db';

/// Current user provider
///
/// Copied from [currentUser].
@ProviderFor(currentUser)
final currentUserProvider = AutoDisposeProvider<WebUserModel?>.internal(
  currentUser,
  name: r'currentUserProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserRef = AutoDisposeProviderRef<WebUserModel?>;
String _$currentSessionHash() => r'97ee52f25eaccf76a4d1a2d31756032f101f8169';

/// Current session provider
///
/// Copied from [currentSession].
@ProviderFor(currentSession)
final currentSessionProvider = AutoDisposeProvider<AuthSessionModel?>.internal(
  currentSession,
  name: r'currentSessionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentSessionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentSessionRef = AutoDisposeProviderRef<AuthSessionModel?>;
String _$isAuthenticatedHash() => r'd267511994819533eb2ade24cb6c2720e399aff7';

/// Authentication status provider
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$isEmailConfirmedHash() => r'2df168f1b9a33a97bfaa129c048419f7fbe759c1';

/// Email confirmation status provider
///
/// Copied from [isEmailConfirmed].
@ProviderFor(isEmailConfirmed)
final isEmailConfirmedProvider = AutoDisposeProvider<bool>.internal(
  isEmailConfirmed,
  name: r'isEmailConfirmedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isEmailConfirmedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsEmailConfirmedRef = AutoDisposeProviderRef<bool>;
String _$webAuthNotifierHash() => r'9fa19d24ed11f47fef063de79e0837959ab934b1';

/// Main web authentication notifier
///
/// Copied from [WebAuthNotifier].
@ProviderFor(WebAuthNotifier)
final webAuthNotifierProvider = AutoDisposeNotifierProvider<WebAuthNotifier,
    AsyncValue<WebAuthState>>.internal(
  WebAuthNotifier.new,
  name: r'webAuthNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webAuthNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WebAuthNotifier = AutoDisposeNotifier<AsyncValue<WebAuthState>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
