import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../app/router/routes.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/ui_constants.dart';
import '../../../../shared/widgets/responsive_layout.dart';
import '../../../auth/providers/web_auth_providers.dart';
import '../../../auth/presentation/widgets/email_verification_banner.dart';

/// Home page for the MeditatingLeo Web Application.
///
/// This page serves as the main landing page for the web application,
/// providing an overview of features and navigation to key sections.
/// It uses responsive design to adapt to different screen sizes.
class HomePage extends ConsumerWidget {
  /// Creates a home page widget.
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    final isEmailConfirmed = ref.watch(isEmailConfirmedProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(AppConstants.appName),
        actions: [
          IconButton(
            icon: const Icon(Icons.brightness_6),
            onPressed: () {
              // TODO: Implement theme toggle
            },
            tooltip: 'Toggle theme',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'logout') {
                _handleLogout(context, ref);
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    const Icon(Icons.logout),
                    const SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
            child: CircleAvatar(
              child: Text(
                currentUser?.name?.substring(0, 1).toUpperCase() ?? 'U',
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Show email verification banner only if:
          // 1. User is authenticated
          // 2. Email is not confirmed
          // 3. User has an email address
          if (currentUser != null &&
              !isEmailConfirmed &&
              currentUser.email.isNotEmpty)
            EmailVerificationBanner(
              email: currentUser.email,
            ),
          Expanded(
            child: ResponsiveLayout(
              mobile: const _MobileHomeLayout(),
              tablet: const _TabletHomeLayout(),
              desktop: const _DesktopHomeLayout(),
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogout(BuildContext context, WidgetRef ref) async {
    final notifier = ref.read(webAuthNotifierProvider.notifier);
    await notifier.signOut();
    if (context.mounted) {
      context.go(AppRoutes.login);
    }
  }
}

/// Mobile layout for the home page.
///
/// Optimized for small screens with vertical stacking
/// and touch-friendly interactions.
class _MobileHomeLayout extends StatelessWidget {
  const _MobileHomeLayout();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: ResponsiveUtils.responsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to Clarity',
            style: theme.textTheme.headlineMedium,
          ),
          const SizedBox(height: UIConstants.spacing16),
          Text(
            'Your journey to mindfulness and self-reflection starts here.',
            style: theme.textTheme.bodyLarge,
          ),
          const SizedBox(height: UIConstants.spacing32),
          _buildFeatureCard(
            context,
            icon: Icons.book,
            title: 'Journal',
            description: 'Reflect on your thoughts and experiences',
          ),
          const SizedBox(height: UIConstants.spacing16),
          _buildFeatureCard(
            context,
            icon: Icons.track_changes,
            title: 'Goals',
            description: 'Set and track your personal goals',
          ),
          const SizedBox(height: UIConstants.spacing16),
          _buildFeatureCard(
            context,
            icon: Icons.timer,
            title: 'Focus Timer',
            description: 'Stay focused with guided sessions',
          ),
        ],
      ),
    );
  }
}

/// Tablet layout for the home page.
///
/// Optimized for medium screens with improved spacing
/// and layout organization.
class _TabletHomeLayout extends StatelessWidget {
  const _TabletHomeLayout();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: ResponsiveUtils.responsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to Clarity',
            style: theme.textTheme.headlineLarge,
          ),
          const SizedBox(height: UIConstants.spacing24),
          Text(
            'Your journey to mindfulness and self-reflection starts here.',
            style: theme.textTheme.bodyLarge,
          ),
          const SizedBox(height: UIConstants.spacing48),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: UIConstants.spacing16,
            mainAxisSpacing: UIConstants.spacing16,
            childAspectRatio: 1.2,
            children: [
              _buildFeatureCard(
                context,
                icon: Icons.book,
                title: 'Journal',
                description: 'Reflect on your thoughts and experiences',
              ),
              _buildFeatureCard(
                context,
                icon: Icons.track_changes,
                title: 'Goals',
                description: 'Set and track your personal goals',
              ),
              _buildFeatureCard(
                context,
                icon: Icons.timer,
                title: 'Focus Timer',
                description: 'Stay focused with guided sessions',
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Desktop layout for the home page.
///
/// Optimized for large screens with enhanced layouts
/// and desktop-specific interactions.
class _DesktopHomeLayout extends StatelessWidget {
  const _DesktopHomeLayout();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: ResponsiveUtils.responsivePadding(context),
      child: Center(
        child: ConstrainedBox(
          constraints:
              const BoxConstraints(maxWidth: UIConstants.maxContentWidth),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome to Clarity',
                style: theme.textTheme.displayMedium,
              ),
              const SizedBox(height: UIConstants.spacing32),
              Text(
                'Your journey to mindfulness and self-reflection starts here.',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: UIConstants.spacing64),
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 3,
                crossAxisSpacing: UIConstants.spacing24,
                mainAxisSpacing: UIConstants.spacing24,
                childAspectRatio: 1.0,
                children: [
                  _buildFeatureCard(
                    context,
                    icon: Icons.book,
                    title: 'Journal',
                    description: 'Reflect on your thoughts and experiences',
                  ),
                  _buildFeatureCard(
                    context,
                    icon: Icons.track_changes,
                    title: 'Goals',
                    description: 'Set and track your personal goals',
                  ),
                  _buildFeatureCard(
                    context,
                    icon: Icons.timer,
                    title: 'Focus Timer',
                    description: 'Stay focused with guided sessions',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Builds a feature card widget.
///
/// Creates a consistent card layout for displaying features
/// with icon, title, and description.
Widget _buildFeatureCard(
  BuildContext context, {
  required IconData icon,
  required String title,
  required String description,
}) {
  final theme = Theme.of(context);

  return Card.filled(
    child: Padding(
      padding: const EdgeInsets.all(UIConstants.spacing24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: UIConstants.iconSizeXLarge,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: UIConstants.spacing16),
          Text(
            title,
            style: theme.textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: UIConstants.spacing8),
          Text(
            description,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}
