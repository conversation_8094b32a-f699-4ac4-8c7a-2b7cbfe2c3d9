# Authentication Flow Guide

## Overview

The MeditatingLeo Web Application supports **both Supabase email verification configurations** automatically, providing the optimal user experience regardless of your Supabase settings.

## Supported Configurations

### 🔧 **Configuration A: Email Verification DISABLED**
*Supabase Setting: Authentication → Settings → Email Confirmation → OFF*

**User Experience:**
1. **Register** → Account created → **Auto-login** → Dashboard
2. **No email verification required**
3. **Immediate access** to the application

**Technical Flow:**
- `signUp()` returns: `(user, session)` ✅
- User is immediately authenticated
- Session is stored in browser
- Router allows access to protected routes

### 📧 **Configuration B: Email Verification ENABLED** *(Default)*
*Supabase Setting: Authentication → Settings → Email Confirmation → ON*

**User Experience:**
1. **Register** → Account created → Success message → **Login page**
2. **Check email** → Click verification link → Email verified
3. **Login manually** → Dashboard (no verification banner)

**Technical Flow:**
- `signUp()` returns: `(user, null)` ❌
- No session until email is verified
- User redirected to login page
- Must verify email before login

## Implementation Details

### Automatic Detection

The application **automatically detects** which configuration is active by checking the signup response:

```dart
// In WebAuthService.signUp()
if (response.session != null) {
  // Email verification is DISABLED
  session = _mapToAuthSession(response.session!);
} else {
  // Email verification is ENABLED
  session = null;
}
```

### Smart Registration Handler

The registration success handler adapts based on the detected configuration:

```dart
void _handleRegistrationSuccess(BuildContext context, WidgetRef ref) {
  final isAuthenticated = ref.read(webAuthNotifierProvider).when(
    data: (state) => state.isAuthenticated,
    loading: () => false,
    error: (_, __) => false,
  );

  if (isAuthenticated) {
    // Configuration A: Auto-login successful
    showSnackBar('Account created successfully! Welcome to Clarity.');
    context.go(AppRoutes.home);
  } else {
    // Configuration B: Email verification required
    showSnackBar('Account created successfully! Please check your email to verify your account, then return to login.');
    context.go(AppRoutes.login);
  }
}
```

### Email Verification Banner

The verification banner intelligently shows only when needed:

```dart
// Shows banner only if:
// 1. User is authenticated AND
// 2. Email is not confirmed AND  
// 3. User has an email address
if (currentUser != null && 
    !isEmailConfirmed && 
    currentUser.email.isNotEmpty) {
  EmailVerificationBanner(email: currentUser.email)
}
```

## Email Verification Process

### Verification Link Handling

When users click email verification links:

1. **URL**: `https://yourapp.com/verify-email?token=xyz`
2. **Route**: `/verify-email` → `EmailVerificationPage`
3. **Process**: Token verification → Success/Error feedback
4. **Redirect**: Auto-redirect to dashboard after verification

### Verification Methods

Users can verify their email through:

1. **Email link** (primary method)
2. **Resend verification** from the banner
3. **Manual verification** on verification page

## Router Configuration

### Authentication Guards

The router handles both configurations seamlessly:

```dart
redirect: (context, state) {
  final authState = ref.read(webAuthNotifierProvider);
  
  // Don't redirect while loading
  if (authState.isLoading) return null;
  
  final isAuthenticated = authState.when(
    data: (state) => state.isAuthenticated,
    loading: () => false,
    error: (_, __) => false,
  );

  // Redirect logic based on authentication state
  if (isAuthenticated && isAuthRoute) return AppRoutes.home;
  if (!isAuthenticated && !isPublicRoute) return AppRoutes.login;
  
  return null; // Allow the route
}
```

### Public Routes

These routes are accessible without authentication:
- `/login` - Login page
- `/register` - Registration page  
- `/forgot-password` - Password reset
- `/verify-email` - Email verification
- `/about` - About page

## Configuration Recommendations

### For Development
- **Disable email verification** for faster testing
- Users can register and immediately access the app
- No email setup required

### For Production
- **Enable email verification** for security
- Ensures valid email addresses
- Prevents spam registrations
- Better user data quality

## Changing Configuration

### In Supabase Dashboard

1. Go to **Authentication** → **Settings**
2. Find **Email Confirmation** setting
3. Toggle **ON/OFF** as needed
4. **No code changes required** - the app adapts automatically!

### Testing Both Configurations

1. **Test with verification OFF:**
   - Register → Should auto-login to dashboard
   - No verification banner should appear

2. **Test with verification ON:**
   - Register → Should redirect to login
   - Check email for verification link
   - Click link → Should verify and redirect to dashboard
   - Login → Should access dashboard without banner

## Error Handling

The application gracefully handles:
- **Network errors** during verification
- **Invalid verification tokens**
- **Expired verification links**
- **Missing email configuration**
- **Session restoration failures**

## Security Considerations

### Email Verification OFF
- ✅ Faster user onboarding
- ❌ Unverified email addresses
- ❌ Potential spam registrations

### Email Verification ON
- ✅ Verified email addresses
- ✅ Better security
- ✅ Spam prevention
- ❌ Additional friction for users

## Troubleshooting

### User Not Auto-Logged In After Registration
- Check Supabase email verification setting
- Verify session is being returned in signup response
- Check browser console for errors

### Verification Banner Always Showing
- Ensure email verification is working
- Check user's `emailConfirmedAt` field
- Verify banner logic conditions

### Router Redirecting to Login
- Check authentication state initialization
- Verify session restoration is working
- Ensure router guards are configured correctly

## Summary

The MeditatingLeo Web Application provides a **seamless authentication experience** that automatically adapts to your Supabase configuration, ensuring optimal user experience regardless of your email verification settings.

**No code changes needed** - just configure Supabase and the app handles the rest! 🎉
