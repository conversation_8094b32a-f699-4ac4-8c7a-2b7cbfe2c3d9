// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_webapp/test/features/auth/presentation/widgets/login_form_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:meditatingleo_webapp/features/auth/data/models/auth_session_model.dart'
    as _i6;
import 'package:meditatingleo_webapp/features/auth/data/models/web_user_model.dart'
    as _i5;
import 'package:meditatingleo_webapp/features/auth/data/repositories/web_auth_repository.dart'
    as _i3;
import 'package:meditatingleo_webapp/shared/models/app_error.dart' as _i7;
import 'package:meditatingleo_webapp/shared/models/result.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:supabase_flutter/supabase_flutter.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResult_0<T, E> extends _i1.SmartFake implements _i2.Result<T, E> {
  _FakeResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [WebAuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebAuthRepository extends _i1.Mock implements _i3.WebAuthRepository {
  MockWebAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<dynamic> get authStateChanges => (super.noSuchMethod(
        Invocation.getter(#authStateChanges),
        returnValue: _i4.Stream<dynamic>.empty(),
      ) as _i4.Stream<dynamic>);

  @override
  _i4.Future<_i2.Result<(_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>>
      signInWithEmail(
    String? email,
    String? password, {
    bool? rememberMe = false,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #signInWithEmail,
              [
                email,
                password,
              ],
              {#rememberMe: rememberMe},
            ),
            returnValue: _i4.Future<
                _i2.Result<(_i5.WebUserModel, _i6.AuthSessionModel),
                    _i7.AppError>>.value(_FakeResult_0<
                (_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>(
              this,
              Invocation.method(
                #signInWithEmail,
                [
                  email,
                  password,
                ],
                {#rememberMe: rememberMe},
              ),
            )),
          ) as _i4.Future<
              _i2
              .Result<(_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<_i5.WebUserModel, _i7.AppError>> signUp(
    String? email,
    String? password,
    String? name,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signUp,
          [
            email,
            password,
            name,
          ],
        ),
        returnValue:
            _i4.Future<_i2.Result<_i5.WebUserModel, _i7.AppError>>.value(
                _FakeResult_0<_i5.WebUserModel, _i7.AppError>(
          this,
          Invocation.method(
            #signUp,
            [
              email,
              password,
              name,
            ],
          ),
        )),
      ) as _i4.Future<_i2.Result<_i5.WebUserModel, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #signOut,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> resetPassword(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [email],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #resetPassword,
            [email],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<_i6.AuthSessionModel, _i7.AppError>> refreshSession() =>
      (super.noSuchMethod(
        Invocation.method(
          #refreshSession,
          [],
        ),
        returnValue:
            _i4.Future<_i2.Result<_i6.AuthSessionModel, _i7.AppError>>.value(
                _FakeResult_0<_i6.AuthSessionModel, _i7.AppError>(
          this,
          Invocation.method(
            #refreshSession,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Result<_i6.AuthSessionModel, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<_i5.WebUserModel?, _i7.AppError>> getCurrentUser() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue:
            _i4.Future<_i2.Result<_i5.WebUserModel?, _i7.AppError>>.value(
                _FakeResult_0<_i5.WebUserModel?, _i7.AppError>(
          this,
          Invocation.method(
            #getCurrentUser,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Result<_i5.WebUserModel?, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<_i6.AuthSessionModel?, _i7.AppError>>
      getCurrentSession() => (super.noSuchMethod(
            Invocation.method(
              #getCurrentSession,
              [],
            ),
            returnValue: _i4
                .Future<_i2.Result<_i6.AuthSessionModel?, _i7.AppError>>.value(
                _FakeResult_0<_i6.AuthSessionModel?, _i7.AppError>(
              this,
              Invocation.method(
                #getCurrentSession,
                [],
              ),
            )),
          ) as _i4.Future<_i2.Result<_i6.AuthSessionModel?, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<_i6.AuthSessionModel?, _i7.AppError>>
      restoreSession() => (super.noSuchMethod(
            Invocation.method(
              #restoreSession,
              [],
            ),
            returnValue: _i4
                .Future<_i2.Result<_i6.AuthSessionModel?, _i7.AppError>>.value(
                _FakeResult_0<_i6.AuthSessionModel?, _i7.AppError>(
              this,
              Invocation.method(
                #restoreSession,
                [],
              ),
            )),
          ) as _i4.Future<_i2.Result<_i6.AuthSessionModel?, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<_i5.WebUserModel, _i7.AppError>> updateProfile({
    String? name,
    String? avatarUrl,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfile,
          [],
          {
            #name: name,
            #avatarUrl: avatarUrl,
          },
        ),
        returnValue:
            _i4.Future<_i2.Result<_i5.WebUserModel, _i7.AppError>>.value(
                _FakeResult_0<_i5.WebUserModel, _i7.AppError>(
          this,
          Invocation.method(
            #updateProfile,
            [],
            {
              #name: name,
              #avatarUrl: avatarUrl,
            },
          ),
        )),
      ) as _i4.Future<_i2.Result<_i5.WebUserModel, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> changePassword(
          String? newPassword) =>
      (super.noSuchMethod(
        Invocation.method(
          #changePassword,
          [newPassword],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #changePassword,
            [newPassword],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> enableRememberMe() =>
      (super.noSuchMethod(
        Invocation.method(
          #enableRememberMe,
          [],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #enableRememberMe,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> disableRememberMe() =>
      (super.noSuchMethod(
        Invocation.method(
          #disableRememberMe,
          [],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #disableRememberMe,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);

  @override
  bool isRememberMeEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isRememberMeEnabled,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<_i2.Result<String, _i7.AppError>> generateCSRFToken() =>
      (super.noSuchMethod(
        Invocation.method(
          #generateCSRFToken,
          [],
        ),
        returnValue: _i4.Future<_i2.Result<String, _i7.AppError>>.value(
            _FakeResult_0<String, _i7.AppError>(
          this,
          Invocation.method(
            #generateCSRFToken,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Result<String, _i7.AppError>>);

  @override
  bool validateCSRFToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #validateCSRFToken,
          [token],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<_i2.Result<(_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>>
      signInWithOAuth({
    required _i8.OAuthProvider? provider,
    String? redirectTo,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #signInWithOAuth,
              [],
              {
                #provider: provider,
                #redirectTo: redirectTo,
              },
            ),
            returnValue: _i4.Future<
                _i2.Result<(_i5.WebUserModel, _i6.AuthSessionModel),
                    _i7.AppError>>.value(_FakeResult_0<
                (_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>(
              this,
              Invocation.method(
                #signInWithOAuth,
                [],
                {
                  #provider: provider,
                  #redirectTo: redirectTo,
                },
              ),
            )),
          ) as _i4.Future<
              _i2
              .Result<(_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<(_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>>
      handleOAuthCallback() => (super.noSuchMethod(
            Invocation.method(
              #handleOAuthCallback,
              [],
            ),
            returnValue: _i4.Future<
                _i2.Result<(_i5.WebUserModel, _i6.AuthSessionModel),
                    _i7.AppError>>.value(_FakeResult_0<
                (_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>(
              this,
              Invocation.method(
                #handleOAuthCallback,
                [],
              ),
            )),
          ) as _i4.Future<
              _i2
              .Result<(_i5.WebUserModel, _i6.AuthSessionModel), _i7.AppError>>);

  @override
  List<_i8.OAuthProvider> getAvailableOAuthProviders() => (super.noSuchMethod(
        Invocation.method(
          #getAvailableOAuthProviders,
          [],
        ),
        returnValue: <_i8.OAuthProvider>[],
      ) as List<_i8.OAuthProvider>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> resendEmailVerification(
          String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #resendEmailVerification,
          [email],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #resendEmailVerification,
            [email],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> verifyEmail(String? token) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyEmail,
          [token],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #verifyEmail,
            [token],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);

  @override
  _i4.Future<_i2.Result<void, _i7.AppError>> clearAllData() =>
      (super.noSuchMethod(
        Invocation.method(
          #clearAllData,
          [],
        ),
        returnValue: _i4.Future<_i2.Result<void, _i7.AppError>>.value(
            _FakeResult_0<void, _i7.AppError>(
          this,
          Invocation.method(
            #clearAllData,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Result<void, _i7.AppError>>);
}
